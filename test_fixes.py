#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

from pathlib import Path
import shutil
from PIL import Image

def create_test_image_with_chinese_name():
    """创建带中文名的测试图片"""
    temp_dir = Path('temp')
    temp_dir.mkdir(exist_ok=True)
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 150), color=(255, 100, 100))
    
    # 保存为中文文件名
    chinese_name = temp_dir / "测试图片_中文名称.jpg"
    img.save(chinese_name, 'JPEG', quality=90)
    
    print(f"创建中文名测试图片: {chinese_name}")
    return chinese_name

def test_image_processing():
    """测试图片处理功能"""
    from main import ImageProcessor
    
    processor = ImageProcessor()
    
    # 获取图片文件
    image_files = processor.get_image_files()
    print(f"找到 {len(image_files)} 个待处理图片")
    
    for image_file in image_files:
        print(f"测试处理: {image_file}")
        
        # 检查是否已处理过
        if processor.is_already_processed(image_file):
            print(f"  - 已处理过，跳过")
            continue
            
        # 验证是否为有效图片
        if not processor.is_valid_image(image_file):
            print(f"  - 无效图片文件，跳过")
            continue
            
        print(f"  - 开始处理...")
        processor.process_image(image_file)

def main():
    """主函数"""
    print("=" * 50)
    print("测试修复后的功能")
    print("=" * 50)
    
    # 创建测试图片
    create_test_image_with_chinese_name()
    
    print("\n开始测试图片处理...")
    test_image_processing()
    
    print("\n检查结果:")
    
    # 检查data/file目录
    file_dir = Path('data/file')
    if file_dir.exists():
        files = list(file_dir.rglob('*'))
        print(f"data/file目录中有 {len(files)} 个文件:")
        for f in files:
            if f.is_file():
                print(f"  - {f}")
    
    # 检查temp目录
    temp_dir = Path('temp')
    if temp_dir.exists():
        files = list(temp_dir.iterdir())
        print(f"temp目录中剩余 {len(files)} 个文件:")
        for f in files:
            print(f"  - {f.name}")

if __name__ == "__main__":
    main()
