#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包安装脚本
自动检测并安装项目所需的依赖包
"""

import sys
import subprocess
import importlib

def check_package(package_name, pip_name=None):
    """检查包是否已安装"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        importlib.import_module(package_name)
        print(f"✓ {pip_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {pip_name} 未安装")
        return False

def install_package(package_name):
    """安装指定的包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package_name} 安装失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("图片处理和加密系统 - 依赖包安装")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    # 定义需要的包（使用本地AVIF工具，无需pillow-avif-plugin）
    required_packages = [
        ("PIL", "Pillow"),
        ("cryptography", "cryptography"),
    ]
    
    print("\n检查依赖包...")
    missing_packages = []
    
    for module_name, pip_name in required_packages:
        if not check_package(module_name, pip_name):
            missing_packages.append(pip_name)
    
    if not missing_packages:
        print("\n所有依赖包已安装！")
        return True
    
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 询问是否安装
    response = input("\n是否现在安装这些包? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("安装已取消")
        return False
    
    # 安装缺失的包
    print("\n开始安装...")
    success_count = 0
    
    for package in missing_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(missing_packages)} 个包安装成功")
    
    if success_count == len(missing_packages):
        print("所有依赖包安装成功！")
        print("现在可以运行主程序了: python main.py")
        return True
    else:
        print("部分包安装失败，请检查网络连接或手动安装")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n安装已取消")
    except Exception as e:
        print(f"\n安装过程中出错: {e}")
        print("请尝试手动安装: pip install -r requirements.txt")
