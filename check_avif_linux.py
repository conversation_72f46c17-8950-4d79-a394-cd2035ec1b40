#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Linux系统上的AVIF工具状态
"""

import platform
import shutil
import subprocess
from pathlib import Path

def check_system_info():
    """检查系统信息"""
    print("=" * 60)
    print("系统信息检查")
    print("=" * 60)
    
    print(f"操作系统: {platform.system()}")
    print(f"系统版本: {platform.release()}")
    print(f"架构: {platform.machine()}")
    print(f"Python版本: {platform.python_version()}")

def check_avif_tools():
    """检查AVIF工具"""
    print("\n" + "=" * 60)
    print("AVIF工具检查")
    print("=" * 60)
    
    system = platform.system().lower()
    
    if system == 'windows':
        encoder_name = 'avifenc.exe'
        decoder_name = 'avifdec.exe'
    else:
        encoder_name = 'avifenc'
        decoder_name = 'avifdec'
    
    print(f"查找工具: {encoder_name}, {decoder_name}")
    
    # 检查当前目录
    print("\n1. 检查当前目录:")
    encoder_local = Path(encoder_name)
    decoder_local = Path(decoder_name)
    
    if encoder_local.exists():
        print(f"✓ 找到本地编码器: {encoder_local}")
    else:
        print(f"✗ 未找到本地编码器: {encoder_name}")
    
    if decoder_local.exists():
        print(f"✓ 找到本地解码器: {decoder_local}")
    else:
        print(f"✗ 未找到本地解码器: {decoder_name}")
    
    # 检查系统PATH
    print("\n2. 检查系统PATH:")
    encoder_system = shutil.which(encoder_name)
    decoder_system = shutil.which(decoder_name)
    
    if encoder_system:
        print(f"✓ 找到系统编码器: {encoder_system}")
    else:
        print(f"✗ 未找到系统编码器: {encoder_name}")
    
    if decoder_system:
        print(f"✓ 找到系统解码器: {decoder_system}")
    else:
        print(f"✗ 未找到系统解码器: {decoder_name}")
    
    # 测试工具是否可用
    print("\n3. 测试工具可用性:")
    
    if encoder_system:
        try:
            result = subprocess.run([encoder_system, '--help'], 
                                  capture_output=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ {encoder_name} 工具可正常运行")
            else:
                print(f"⚠ {encoder_name} 工具运行异常")
        except Exception as e:
            print(f"✗ {encoder_name} 工具测试失败: {e}")
    
    if decoder_system:
        try:
            result = subprocess.run([decoder_system, '--help'], 
                                  capture_output=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ {decoder_name} 工具可正常运行")
            else:
                print(f"⚠ {decoder_name} 工具运行异常")
        except Exception as e:
            print(f"✗ {decoder_name} 工具测试失败: {e}")
    
    return encoder_system or encoder_local.exists(), decoder_system or decoder_local.exists()

def provide_installation_guide():
    """提供安装指南"""
    print("\n" + "=" * 60)
    print("AVIF工具安装指南")
    print("=" * 60)
    
    system = platform.system().lower()
    
    if system == 'linux':
        print("Linux系统安装AVIF工具:")
        print("\n方法1 - 使用包管理器:")
        print("  Ubuntu/Debian:")
        print("    sudo apt update")
        print("    sudo apt install libavif-bin")
        print("  或者:")
        print("    sudo apt install avif-tools")
        print("\n  CentOS/RHEL/Fedora:")
        print("    sudo dnf install libavif-tools")
        print("  或者:")
        print("    sudo yum install libavif-tools")
        
        print("\n方法2 - 从源码编译:")
        print("  git clone https://github.com/AOMediaCodec/libavif.git")
        print("  cd libavif")
        print("  mkdir build && cd build")
        print("  cmake ..")
        print("  make -j$(nproc)")
        print("  sudo make install")
        
        print("\n方法3 - 下载预编译二进制文件:")
        print("  从 https://github.com/AOMediaCodec/libavif/releases 下载")
        print("  将 avifenc 和 avifdec 放在程序目录中")
        
    elif system == 'windows':
        print("Windows系统安装AVIF工具:")
        print("  从 https://github.com/AOMediaCodec/libavif/releases 下载")
        print("  将 avifenc.exe 和 avifdec.exe 放在程序目录中")
    
    else:
        print(f"暂不支持 {system} 系统的安装指南")

def test_main_program():
    """测试主程序的AVIF检测"""
    print("\n" + "=" * 60)
    print("主程序AVIF检测测试")
    print("=" * 60)
    
    try:
        # 导入主程序的检测函数
        import sys
        sys.path.insert(0, '.')
        
        from main import detect_avif_tools
        
        encoder, decoder, available = detect_avif_tools()
        
        if available:
            print(f"✓ 主程序检测成功:")
            print(f"  编码器: {encoder}")
            print(f"  解码器: {decoder}")
        else:
            print("✗ 主程序检测失败，AVIF功能不可用")
            
    except Exception as e:
        print(f"✗ 主程序检测异常: {e}")

def main():
    """主函数"""
    print("Linux AVIF工具检查")
    
    # 检查系统信息
    check_system_info()
    
    # 检查AVIF工具
    encoder_ok, decoder_ok = check_avif_tools()
    
    # 测试主程序检测
    test_main_program()
    
    # 如果工具不可用，提供安装指南
    if not (encoder_ok and decoder_ok):
        provide_installation_guide()
    
    print("\n" + "=" * 60)
    if encoder_ok and decoder_ok:
        print("✓ AVIF工具检查完成，工具可用")
    else:
        print("✗ AVIF工具不可用，请按照上述指南安装")
    print("=" * 60)

if __name__ == "__main__":
    main()
