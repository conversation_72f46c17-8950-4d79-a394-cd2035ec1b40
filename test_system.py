#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
用于测试图片处理和加密系统的功能
"""

import os
import time
import shutil
from pathlib import Path
from PIL import Image

def create_test_images():
    """创建测试图片"""
    temp_dir = Path('temp')
    temp_dir.mkdir(exist_ok=True)
    
    # 创建不同格式的测试图片
    test_images = [
        ('test_image_1.jpg', 'RGB', (300, 200), (255, 0, 0)),      # 红色JPG
        ('test_image_2.png', 'RGBA', (250, 300), (0, 255, 0, 128)), # 半透明绿色PNG
        ('test_image_3.webp', 'RGB', (400, 300), (0, 0, 255)),     # 蓝色WebP
    ]
    
    created_files = []
    
    for filename, mode, size, color in test_images:
        file_path = temp_dir / filename
        
        # 创建纯色图片
        img = Image.new(mode, size, color)
        
        # 添加一些文本以便识别
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img)
            
            # 尝试使用默认字体
            try:
                font = ImageFont.load_default()
            except:
                font = None
            
            text = f"Test Image\n{filename}"
            if font:
                draw.text((10, 10), text, fill=(255, 255, 255) if mode == 'RGB' else (255, 255, 255, 255), font=font)
            else:
                draw.text((10, 10), text, fill=(255, 255, 255) if mode == 'RGB' else (255, 255, 255, 255))
                
        except Exception as e:
            print(f"添加文本失败: {e}")
        
        # 保存图片
        if filename.endswith('.webp'):
            img.save(file_path, 'WebP', quality=90)
        elif filename.endswith('.png'):
            img.save(file_path, 'PNG')
        else:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            img.save(file_path, 'JPEG', quality=90)
        
        created_files.append(file_path)
        print(f"创建测试图片: {file_path}")
    
    return created_files

def run_test():
    """运行测试"""
    print("=" * 60)
    print("图片处理和加密系统测试")
    print("=" * 60)
    
    # 清理之前的测试文件
    temp_dir = Path('temp')
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
        print("清理旧的测试文件")
    
    # 删除旧的数据库文件
    db_file = Path('image_encryption.db')
    if db_file.exists():
        db_file.unlink()
        print("清理旧的数据库文件")
    
    print("\n1. 创建测试图片...")
    test_files = create_test_images()
    
    print(f"\n创建了 {len(test_files)} 个测试图片:")
    for file_path in test_files:
        size = file_path.stat().st_size
        print(f"  - {file_path.name}: {size} bytes")
    
    print("\n2. 测试提示:")
    print("   - 现在可以运行 'python main.py' 来启动图片处理系统")
    print("   - 系统会自动检测并处理temp目录中的图片")
    print("   - 处理完成后，可以使用 'python decrypt_tool.py --list' 查看加密文件")
    print("   - 使用 'python decrypt_tool.py --all decrypted' 解密所有文件")
    
    print("\n3. 预期结果:")
    print("   - 每个原图片会被转换为加密的AVIF格式")
    print("   - 会生成对应的加密缩略图（_tagimg.jpg）")
    print("   - 加密信息会保存在image_encryption.db数据库中")
    
    print("\n测试图片已准备就绪！")

def check_dependencies():
    """检查依赖包是否安装"""
    print("检查依赖包...")
    
    required_packages = [
        ('PIL', 'Pillow'),
        ('cryptography', 'cryptography'),
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            print(f"✓ {pip_name} 已安装")
        except ImportError:
            print(f"✗ {pip_name} 未安装")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("所有依赖包已安装！")
    return True

def main():
    """主函数"""
    if not check_dependencies():
        return
    
    print()
    run_test()

if __name__ == "__main__":
    main()
