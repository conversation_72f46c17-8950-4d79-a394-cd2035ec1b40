#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健壮性改进
"""

from pathlib import Path
from PIL import Image
import os

def create_test_scenarios():
    """创建各种测试场景"""
    temp_dir = Path('temp')
    temp_dir.mkdir(exist_ok=True)
    
    scenarios = []
    
    # 场景1: 正常的JPG图片
    print("创建场景1: 正常JPG图片")
    img1 = Image.new('RGB', (200, 150), (255, 100, 100))
    path1 = temp_dir / "正常图片.jpg"
    img1.save(path1, 'JPEG', quality=90)
    scenarios.append(("正常JPG", path1))
    
    # 场景2: WebP图片（可能转换失败）
    print("创建场景2: WebP图片")
    img2 = Image.new('RGB', (150, 200), (100, 255, 100))
    path2 = temp_dir / "webp图片.webp"
    img2.save(path2, 'WebP', quality=90)
    scenarios.append(("WebP图片", path2))
    
    # 场景3: 很小的图片（可能缩略图生成有问题）
    print("创建场景3: 小尺寸图片")
    img3 = Image.new('RGB', (10, 10), (100, 100, 255))
    path3 = temp_dir / "小图片.png"
    img3.save(path3, 'PNG')
    scenarios.append(("小图片", path3))
    
    # 场景4: 损坏的图片文件（创建一个假的图片文件）
    print("创建场景4: 损坏的图片文件")
    path4 = temp_dir / "damaged_image.jpg"
    with open(path4, 'wb') as f:
        f.write(b"This is not a real image file")
    scenarios.append(("损坏图片", path4))
    
    return scenarios

def test_processing():
    """测试处理流程"""
    from main import ImageProcessor
    
    processor = ImageProcessor()
    
    print("\n" + "="*50)
    print("开始测试健壮性处理")
    print("="*50)
    
    # 获取所有图片文件
    image_files = processor.get_image_files()
    print(f"检测到 {len(image_files)} 个图片文件")
    
    for image_file in image_files:
        print(f"\n{'='*30}")
        print(f"测试文件: {image_file}")
        print(f"{'='*30}")
        
        # 验证文件
        if not processor.is_valid_image(image_file):
            print(f"⚠ 无效图片文件，应该被跳过")
            continue
            
        # 处理文件
        processor.process_image(image_file)

def check_results():
    """检查处理结果"""
    print("\n" + "="*50)
    print("处理结果检查")
    print("="*50)
    
    # 检查temp目录
    temp_dir = Path('temp')
    if temp_dir.exists():
        temp_files = list(temp_dir.iterdir())
        print(f"temp目录剩余文件: {len(temp_files)}")
        for f in temp_files:
            print(f"  - {f.name}")
    
    # 检查file目录
    file_dir = Path('file')
    if file_dir.exists():
        file_files = list(file_dir.iterdir())
        print(f"file目录文件: {len(file_files)}")
        for f in sorted(file_files):
            size = f.stat().st_size
            print(f"  - {f.name} ({size} bytes)")
    
    # 检查数据库
    try:
        import sqlite3
        conn = sqlite3.connect('image_encryption.db')
        cursor = conn.cursor()
        cursor.execute('SELECT file_path, sha1_hash FROM encrypted_images ORDER BY created_at DESC LIMIT 10')
        records = cursor.fetchall()
        print(f"最新数据库记录: {len(records)} 条")
        for path, sha1 in records:
            print(f"  - {path} (SHA1: {sha1[:16]}...)")
        conn.close()
    except Exception as e:
        print(f"数据库检查失败: {e}")

def main():
    """主函数"""
    print("健壮性测试 - 处理各种异常情况")
    print("="*60)
    
    # 创建测试场景
    scenarios = create_test_scenarios()
    print(f"\n创建了 {len(scenarios)} 个测试场景")
    
    # 测试处理
    test_processing()
    
    # 检查结果
    check_results()
    
    print("\n测试完成！")
    print("预期结果:")
    print("- 正常图片应该被成功处理")
    print("- 损坏图片应该被跳过")
    print("- 转换失败的图片应该使用原格式加密")
    print("- 缩略图失败不应该影响主图处理")

if __name__ == "__main__":
    main()
