#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序功能
"""

from pathlib import Path
import sys

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_main_import():
    """测试主程序导入"""
    try:
        import main
        print("✓ 主程序导入成功")
        return True
    except Exception as e:
        print(f"✗ 主程序导入失败: {e}")
        return False

def test_image_processor():
    """测试ImageProcessor类"""
    try:
        from main import ImageProcessor
        processor = ImageProcessor()
        print("✓ ImageProcessor创建成功")
        
        # 测试获取图片文件
        image_files = processor.get_image_files()
        print(f"✓ 找到 {len(image_files)} 个图片文件")
        
        if image_files:
            # 测试处理单个图片
            test_image = image_files[0]
            print(f"测试处理图片: {test_image}")
            
            # 只测试缩略图生成
            thumbnail = processor.generate_thumbnail(test_image)
            if thumbnail and thumbnail.exists():
                print(f"✓ 缩略图生成成功: {thumbnail}")
            else:
                print("✗ 缩略图生成失败")
                
            # 测试AVIF转换
            avif_file = processor.convert_to_avif(test_image)
            if avif_file and avif_file.exists():
                print(f"✓ AVIF转换成功: {avif_file}")
            else:
                print("✗ AVIF转换失败")
        
        return True
        
    except Exception as e:
        print(f"✗ ImageProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("主程序功能测试")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    if not test_main_import():
        success = False
    
    print()
    
    # 测试ImageProcessor
    if not test_image_processor():
        success = False
    
    print()
    if success:
        print("✓ 所有测试通过!")
    else:
        print("✗ 部分测试失败!")

if __name__ == "__main__":
    main()
