#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
"""

from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_test_images():
    """创建多个测试图片，包括中文名"""
    temp_dir = Path('temp')
    temp_dir.mkdir(exist_ok=True)
    
    test_images = [
        ('测试图片1.jpg', 'RGB', (300, 200), (255, 100, 100)),
        ('test_image_english.png', 'RGBA', (250, 250), (100, 255, 100, 200)),
        ('图片_中文_测试.webp', 'RGB', (200, 300), (100, 100, 255)),
    ]
    
    created_files = []
    
    for filename, mode, size, color in test_images:
        file_path = temp_dir / filename
        
        # 创建图片
        img = Image.new(mode, size, color)
        
        # 添加文本标识
        try:
            draw = ImageDraw.Draw(img)
            text = f"Test\n{filename}"
            draw.text((10, 10), text, fill=(255, 255, 255) if mode == 'RGB' else (255, 255, 255, 255))
        except:
            pass
        
        # 保存图片
        if filename.endswith('.webp'):
            img.save(file_path, 'WebP', quality=90)
        elif filename.endswith('.png'):
            img.save(file_path, 'PNG')
        else:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            img.save(file_path, 'JPEG', quality=90)
        
        created_files.append(file_path)
        print(f"创建测试图片: {file_path} ({file_path.stat().st_size} bytes)")
    
    return created_files

def test_processing():
    """测试处理流程"""
    from main import ImageProcessor
    
    processor = ImageProcessor()
    
    print("\n开始处理图片...")
    image_files = processor.get_image_files()
    print(f"找到 {len(image_files)} 个待处理图片")
    
    for image_file in image_files:
        print(f"\n处理: {image_file}")
        processor.process_image(image_file)

def check_results():
    """检查处理结果"""
    print("\n" + "="*50)
    print("处理结果检查")
    print("="*50)
    
    # 检查temp目录
    temp_dir = Path('temp')
    if temp_dir.exists():
        temp_files = list(temp_dir.iterdir())
        print(f"temp目录剩余文件: {len(temp_files)}")
        for f in temp_files:
            print(f"  - {f.name}")
    
    # 检查file目录
    file_dir = Path('file')
    if file_dir.exists():
        file_files = list(file_dir.iterdir())
        print(f"file目录文件: {len(file_files)}")
        for f in file_files:
            print(f"  - {f.name}")
    
    # 检查数据库
    try:
        import sqlite3
        conn = sqlite3.connect('image_encryption.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM encrypted_images')
        count = cursor.fetchone()[0]
        print(f"数据库记录: {count} 条")
        conn.close()
    except Exception as e:
        print(f"数据库检查失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("最终功能测试")
    print("=" * 60)
    
    # 创建测试图片
    print("1. 创建测试图片...")
    created_files = create_test_images()
    
    # 测试处理
    print("\n2. 测试图片处理...")
    test_processing()
    
    # 检查结果
    print("\n3. 检查处理结果...")
    check_results()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
