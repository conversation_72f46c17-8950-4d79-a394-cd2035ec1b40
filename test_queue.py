#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库队列系统
"""

from pathlib import Path
import time
import shutil
from PIL import Image

def create_test_image():
    """创建测试图片"""
    temp_dir = Path('temp')
    temp_dir.mkdir(exist_ok=True)
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 150), color=(100, 150, 200))
    test_path = temp_dir / "queue_test.jpg"
    img.save(test_path, 'JPEG', quality=90)
    
    print(f"创建测试图片: {test_path}")
    return test_path

def test_database_queue():
    """测试数据库队列系统"""
    print("=" * 60)
    print("数据库队列系统测试")
    print("=" * 60)
    
    # 清理旧的数据库文件
    db_file = Path('image_encryption.db')
    if db_file.exists():
        db_file.unlink()
        print("✓ 清理旧的数据库文件")
    
    # 创建测试图片
    test_image = create_test_image()
    
    # 导入并测试主程序
    print("\n测试主程序初始化...")
    from main import ImageProcessor
    
    processor = ImageProcessor()
    print("✓ ImageProcessor初始化完成")
    
    # 检查数据库是否被创建
    if db_file.exists():
        print("✓ 数据库文件已自动创建")
    else:
        print("✗ 数据库文件未创建")
        return
    
    # 测试图片处理
    print("\n测试图片处理...")
    image_files = processor.get_image_files()
    print(f"找到 {len(image_files)} 个图片文件")
    
    if image_files:
        test_file = image_files[0]
        print(f"处理测试文件: {test_file}")
        
        # 处理图片
        processor.process_image(test_file)
        
        # 等待队列处理完成
        print("等待数据库队列处理...")
        time.sleep(3)  # 给队列一些时间处理
        
        # 检查数据库记录
        print("\n检查数据库记录...")
        check_database_records()
    
    # 停止数据库队列
    print("\n停止数据库队列...")
    from database_queue import stop_database_queue
    stop_database_queue()

def check_database_records():
    """检查数据库记录"""
    try:
        import sqlite3
        conn = sqlite3.connect('image_encryption.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(encrypted_images)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"✓ 数据库表结构: {columns}")
        
        # 检查记录数量
        cursor.execute("SELECT COUNT(*) FROM encrypted_images")
        count = cursor.fetchone()[0]
        print(f"✓ 数据库记录数量: {count}")
        
        # 显示最新记录
        cursor.execute("SELECT file_path, sha1_hash, created_at FROM encrypted_images ORDER BY created_at DESC LIMIT 5")
        records = cursor.fetchall()
        
        if records:
            print("✓ 最新数据库记录:")
            for file_path, sha1, created_at in records:
                print(f"  - {file_path} (SHA1: {sha1[:16]}...) [{created_at}]")
        else:
            print("⚠ 没有找到数据库记录")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 检查数据库记录失败: {e}")

def test_concurrent_access():
    """测试并发访问"""
    print("\n" + "=" * 60)
    print("并发访问测试")
    print("=" * 60)
    
    from database_queue import get_database_queue
    import threading
    
    # 启动数据库队列
    db_queue = get_database_queue()
    db_queue.start()
    
    def worker(worker_id):
        """工作线程"""
        for i in range(5):
            db_queue.save_encrypted_image(
                file_path=f"test/worker_{worker_id}_file_{i}.jpg",
                password=f"password_{worker_id}_{i}",
                iv=f"iv_{worker_id}_{i}",
                sha1_hash=f"sha1_{worker_id}_{i}",
                txt=f"worker {worker_id} file {i}"
            )
            time.sleep(0.1)  # 短暂延迟
    
    # 创建多个工作线程
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 等待队列处理完成
    time.sleep(2)
    
    # 检查结果
    check_database_records()
    
    # 停止队列
    db_queue.stop()

def main():
    """主函数"""
    print("数据库队列系统测试")
    
    try:
        # 基本功能测试
        test_database_queue()
        
        # 并发访问测试
        test_concurrent_access()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
